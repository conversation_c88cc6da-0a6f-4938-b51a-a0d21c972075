<template>
  <view class="profile-container">
    <!-- 简洁用户头部区域 -->
    <view class="user-header-section fade-in">
      <view class="user-profile-card">
        <view class="user-avatar-area">
          <view class="avatar-container">
            <image class="user-avatar" :src="userInfo.avatar || '/static/default-avatar.svg'" mode="aspectFill"
              @load="onAvatarLoad" />
            <view v-if="userInfo.isLogin" class="status-indicator pulse"></view>
          </view>
        </view>

        <view class="user-details">
          <text class="user-name">{{ userInfo.nickname || '未登录用户' }}</text>
          <text class="user-subtitle">{{ getUserSubtitle() }}</text>

          <button v-if="!userInfo.isLogin" @click="handleWechatLogin" class="btn btn-primary compact-login-btn"
            :disabled="isLoggingIn">
            <text v-if="!isLoggingIn" class="btn-icon">👤</text>
            <text v-if="isLoggingIn" class="btn-icon loading-spin">⏳</text>
            <text>{{ isLoggingIn ? '登录中...' : '微信登录' }}</text>
          </button>
        </view>
      </view>
    </view>

    <!-- 快速操作区域 -->
    <view class="quick-actions-section slide-up">
      <view class="section-title-wrapper">
        <text class="section-title">快速操作</text>
      </view>

      <view class="quick-actions-grid">
        <view class="quick-action-card touch-feedback" @click="navigateTo('/pages/upload/upload')">
          <view class="action-icon-wrapper primary">
            <text class="action-icon">�</text>
          </view>
          <text class="action-title">开始制作</text>
          <text class="action-desc">上传视频制作字幕</text>
        </view>

        <view class="quick-action-card touch-feedback" @click="navigateTo('/pages/history/history')">
          <view class="action-icon-wrapper secondary">
            <text class="action-icon">📋</text>
          </view>
          <text class="action-title">历史记录</text>
          <text class="action-desc">查看处理历史</text>
        </view>
      </view>
    </view>

    <!-- 个人数据概览 -->
    <view v-if="userInfo.isLogin" class="stats-overview-section">
      <view class="section-title-wrapper">
        <text class="section-title">使用概览</text>
        <text class="section-subtitle">您的使用数据统计</text>
      </view>

      <view class="stats-cards-grid">
        <view class="stat-card" v-for="(stat, index) in displayStats" :key="index">
          <view class="stat-icon-container" :class="stat.colorClass">
            <text class="stat-icon">{{ stat.icon }}</text>
          </view>
          <view class="stat-content">
            <text class="stat-value">{{ stat.value }}</text>
            <text class="stat-label">{{ stat.label }}</text>
          </view>
        </view>
      </view>
    </view>

    <!-- 设置与帮助 -->
    <view class="settings-section">
      <view class="section-title-wrapper">
        <text class="section-title">设置与帮助</text>
      </view>

      <view class="settings-card">
        <view class="settings-group">
          <view class="setting-item" @click="showSettings">
            <view class="setting-icon-wrapper">
              <text class="setting-icon">⚙️</text>
            </view>
            <view class="setting-content">
              <text class="setting-title">个性化设置</text>
              <text class="setting-desc">自定义您的使用偏好</text>
            </view>
            <text class="setting-arrow">›</text>
          </view>

          <view class="setting-item" @click="showAbout">
            <view class="setting-icon-wrapper">
              <text class="setting-icon">ℹ️</text>
            </view>
            <view class="setting-content">
              <text class="setting-title">关于我们</text>
              <text class="setting-desc">了解产品信息</text>
            </view>
            <text class="setting-arrow">›</text>
          </view>

          <view class="setting-item" @click="showHelp">
            <view class="setting-icon-wrapper">
              <text class="setting-icon">❓</text>
            </view>
            <view class="setting-content">
              <text class="setting-title">帮助与反馈</text>
              <text class="setting-desc">获取帮助和技术支持</text>
            </view>
            <text class="setting-arrow">›</text>
          </view>
        </view>
      </view>
    </view>

    <!-- 版本信息 -->
    <view class="app-info-section">
      <text class="app-version">智能字幕胶囊 v1.0.0</text>
      <text class="app-copyright">© 2024 All Rights Reserved</text>
    </view>
  </view>
</template>

<script setup lang="ts">
import { ref, onMounted } from 'vue'
import { getDeviceId } from '@/utils/common'
import { getWechatOpenid, updateUserInfo } from '@/utils/api'

// 用户信息
const userInfo = ref({
  nickname: '',
  avatar: '',
  deviceId: '',
  openid: '',
  isLogin: false
})

// 统计信息
const stats = ref({
  totalTasks: 0,
  completedTasks: 0,
  totalDuration: 0,
  totalSize: 0
})

// 交互状态
const isLoggingIn = ref(false)

// 页面加载时获取用户信息和统计数据
onMounted(async () => {
  await loadUserInfo()
  await loadStats()
})

// 加载用户信息
const loadUserInfo = async () => {
  try {
    const deviceId = getDeviceId()
    userInfo.value.deviceId = deviceId

    // 尝试从本地存储获取用户信息
    const savedUserInfo = uni.getStorageSync('userInfo')
    if (savedUserInfo) {
      userInfo.value.nickname = savedUserInfo.nickname
      userInfo.value.avatar = savedUserInfo.avatar
      userInfo.value.openid = savedUserInfo.openid || ''
      userInfo.value.isLogin = true
    } else {
      userInfo.value.nickname = `用户${deviceId.slice(-6)}`
    }
  } catch (error) {
    console.error('加载用户信息失败:', error)
  }
}

// 获取用户副标题
const getUserSubtitle = (): string => {
  if (userInfo.value.isLogin) {
    return `ID: ${userInfo.value.deviceId?.slice(-8) || 'N/A'}`
  }
  return '点击登录获取更多功能'
}

// 显示统计数据
const displayStats = ref([
  {
    icon: '📊',
    value: '0',
    label: '处理任务',
    colorClass: 'primary'
  },
  {
    icon: '✅',
    value: '0',
    label: '已完成',
    colorClass: 'success'
  },
  {
    icon: '⏱️',
    value: '0分钟',
    label: '总时长',
    colorClass: 'warning'
  },
  {
    icon: '📁',
    value: '0B',
    label: '处理文件',
    colorClass: 'info'
  }
])

// 格式化文件大小
const formatFileSize = (size: number): string => {
  if (!size) return '0B'
  if (size < 1024) return size + 'B'
  if (size < 1024 * 1024) return (size / 1024).toFixed(1) + 'KB'
  if (size < 1024 * 1024 * 1024) return (size / (1024 * 1024)).toFixed(1) + 'MB'
  return (size / (1024 * 1024 * 1024)).toFixed(1) + 'GB'
}

// 加载统计数据
const loadStats = async () => {
  try {
    // 更新显示统计数据
    displayStats.value[0].value = stats.value.totalTasks.toString()
    displayStats.value[1].value = stats.value.completedTasks.toString()
    displayStats.value[2].value = `${stats.value.totalDuration}分钟`
    displayStats.value[3].value = formatFileSize(stats.value.totalSize)
  } catch (error) {
    console.error('加载统计数据失败:', error)
  }
}

// 微信登录
const handleWechatLogin = async () => {
  try {
    isLoggingIn.value = true
    uni.showLoading({ title: '登录中...' })

    // 第一步：先获取用户信息授权（必须在用户点击事件中立即调用）
    const userProfile = await new Promise<any>((resolve, reject) => {
      uni.getUserProfile({
        desc: '用于完善用户资料',
        success: resolve,
        fail: reject
      })
    })

    // 第二步：调用 uni.login 获取 code
    const loginRes = await new Promise<any>((resolve, reject) => {
      uni.login({
        provider: 'weixin',
        success: resolve,
        fail: reject
      })
    })

    if (!loginRes.code) {
      throw new Error('获取登录凭证失败')
    }

    console.log('获取到微信登录code:', loginRes.code)

    // 第三步：调用服务端接口获取 openid
    uni.showLoading({ title: '获取用户信息中...' })

    const openidResult = await getWechatOpenid(loginRes.code)
    console.log('获取openid结果:', openidResult)

    // 验证返回结果
    if (openidResult.errCode !== 0) {
      throw new Error(openidResult.errMsg || '获取openid失败')
    }

    if (!openidResult.data || !openidResult.data.openid) {
      throw new Error('获取用户openid失败')
    }

    const { openid, isNewUser } = openidResult.data

    // 第四步：更新服务端用户信息
    if (userProfile.userInfo.nickName || userProfile.userInfo.avatarUrl) {
      try {
        await updateUserInfo(
          openid,
          userProfile.userInfo.nickName,
          userProfile.userInfo.avatarUrl
        )
        console.log('用户信息更新成功')
      } catch (updateError) {
        console.warn('更新用户信息失败，但不影响登录:', updateError)
      }
    }

    // 第五步：更新本地用户信息
    userInfo.value.nickname = userProfile.userInfo.nickName
    userInfo.value.avatar = userProfile.userInfo.avatarUrl
    userInfo.value.openid = openid
    userInfo.value.isLogin = true

    // 保存用户信息到本地存储
    const userInfoToSave = {
      nickname: userInfo.value.nickname,
      avatar: userInfo.value.avatar,
      openid: userInfo.value.openid,
      loginTime: Date.now(),
      isNewUser: isNewUser
    }

    uni.setStorageSync('userInfo', userInfoToSave)

    uni.hideLoading()
    isLoggingIn.value = false

    // 显示登录成功提示
    const successMessage = isNewUser ? '注册成功' : '登录成功'
    uni.showToast({
      title: successMessage,
      icon: 'success'
    })

    console.log('微信登录完成:', {
      openid: openid,
      nickname: userInfo.value.nickname,
      isNewUser: isNewUser
    })

  } catch (error: any) {
    uni.hideLoading()
    isLoggingIn.value = false
    console.error('微信登录失败:', error)

    let errorMessage = '登录失败'

    // 处理不同类型的错误
    if (error.errMsg) {
      if (error.errMsg.includes('auth deny')) {
        errorMessage = '用户拒绝授权'
      } else if (error.errMsg.includes('auth cancel')) {
        errorMessage = '用户取消登录'
      } else if (error.errMsg.includes('can only be invoked by user TAP gesture')) {
        errorMessage = '请直接点击登录按钮进行授权'
      }
    } else if (error.message) {
      if (error.message.includes('获取登录凭证失败')) {
        errorMessage = '获取登录凭证失败，请重试'
      } else if (error.message.includes('获取用户openid失败')) {
        errorMessage = '获取用户信息失败，请重试'
      } else if (error.message.includes('网络')) {
        errorMessage = '网络连接失败，请检查网络'
      }
    }

    uni.showToast({
      title: errorMessage,
      icon: 'none',
      duration: 3000
    })
  }
}

// 头像加载完成
const onAvatarLoad = () => {
  console.log('头像加载完成')
}

// 页面导航
const navigateTo = (url: string) => {
  uni.navigateTo({ url })
}

// 显示关于信息
const showAbout = () => {
  uni.showModal({
    title: '关于我们',
    content: '智能字幕胶囊是一款AI驱动的视频字幕生成工具，支持视频添加字幕、字幕翻译、视频去水印等功能。',
    showCancel: false
  })
}

// 显示设置
const showSettings = () => {
  uni.showModal({
    title: '设置',
    content: '设置功能正在开发中，敬请期待！',
    showCancel: false
  })
}

// 显示帮助信息
const showHelp = () => {
  uni.showModal({
    title: '帮助与反馈',
    content: '如有问题或建议，请联系我们的客服团队。我们将竭诚为您服务！',
    showCancel: false
  })
}
</script>


<style lang="scss" scoped>
/* 使用设计系统变量的现代化样式 */
.profile-container {
  min-height: 100vh;
  background-color: $uni-bg-color-page;
  padding-bottom: $spacing-24;
}

/* ==================== 简洁用户头部区域 ==================== */
.user-header-section {
  padding: $spacing-6 $spacing-4 $spacing-8;
  background: linear-gradient(135deg, $primary-600, $primary-700);
  position: relative;
  overflow: hidden;
}

.user-header-section::before {
  content: '';
  position: absolute;
  top: 0;
  left: 0;
  right: 0;
  bottom: 0;
  background: radial-gradient(circle at 30% 20%, rgba(255, 255, 255, 0.1) 0%, transparent 50%);
  pointer-events: none;
}

.user-profile-card {
  position: relative;
  z-index: 1;
  display: flex;
  align-items: center;
  gap: $spacing-4;
}

.user-avatar-area {
  flex-shrink: 0;
}

.avatar-container {
  position: relative;
}

.user-avatar {
  width: 96rpx;
  height: 96rpx;
  border-radius: $radius-full;
  border: 3rpx solid rgba(255, 255, 255, 0.3);
  box-shadow: $shadow-md;
}

.status-indicator {
  position: absolute;
  bottom: 4rpx;
  right: 4rpx;
  width: 20rpx;
  height: 20rpx;
  background-color: $success-500;
  border-radius: $radius-full;
  border: 2rpx solid white;
}

.user-details {
  flex: 1;
  display: flex;
  flex-direction: column;
  gap: $spacing-1;
}

.user-name {
  font-size: $font-size-xl;
  font-weight: $font-weight-bold;
  color: $uni-text-color-inverse;
  line-height: $line-height-tight;
}

.user-subtitle {
  font-size: $font-size-sm;
  color: rgba(255, 255, 255, 0.8);
  line-height: $line-height-normal;
}

.compact-login-btn {
  margin-top: $spacing-2;
  padding: $spacing-2 $spacing-4;
  font-size: $font-size-sm;
  border-radius: $radius-lg;
  box-shadow: $shadow-sm;
  align-self: flex-start;
}

.compact-login-btn .btn-icon {
  margin-right: $spacing-1;
  font-size: $font-size-sm;
}

/* ==================== 快速操作区域 ==================== */
.quick-actions-section {
  padding: $spacing-6 $spacing-4;
}

.section-title-wrapper {
  margin-bottom: $spacing-4;
}

.section-title {
  font-size: $font-size-lg;
  font-weight: $font-weight-semibold;
  color: $uni-text-color;
  line-height: $line-height-tight;
}

.section-subtitle {
  font-size: $font-size-sm;
  color: $uni-text-color-secondary;
  margin-top: $spacing-1;
  line-height: $line-height-normal;
}

.quick-actions-grid {
  display: grid;
  grid-template-columns: repeat(2, 1fr);
  gap: $spacing-4;
}

.quick-action-card {
  background-color: $uni-bg-color;
  border-radius: $radius-xl;
  padding: $spacing-6;
  box-shadow: $shadow-sm;
  transition: all $transition-base;
  cursor: pointer;
  display: flex;
  flex-direction: column;
  align-items: center;
  text-align: center;
  gap: $spacing-3;
}

.quick-action-card:hover {
  box-shadow: $shadow-md;
  transform: translateY(-2rpx);
}

.quick-action-card:active {
  transform: translateY(0);
  box-shadow: $shadow-sm;
}

.action-icon-wrapper {
  width: 80rpx;
  height: 80rpx;
  border-radius: $radius-2xl;
  display: flex;
  align-items: center;
  justify-content: center;
  margin-bottom: $spacing-2;
}

.action-icon-wrapper.primary {
  background: linear-gradient(135deg, $primary-500, $primary-600);
}

.action-icon-wrapper.secondary {
  background: linear-gradient(135deg, $neutral-400, $neutral-500);
}

.action-icon {
  font-size: $font-size-2xl;
}

.action-title {
  font-size: $font-size-base;
  font-weight: $font-weight-semibold;
  color: $uni-text-color;
  line-height: $line-height-tight;
}

.action-desc {
  font-size: $font-size-xs;
  color: $uni-text-color-secondary;
  line-height: $line-height-normal;
}

/* ==================== 个人数据概览 ==================== */
.stats-overview-section {
  padding: 0 $spacing-4 $spacing-6;
}

.stats-cards-grid {
  display: grid;
  grid-template-columns: repeat(2, 1fr);
  gap: $spacing-4;
}

.stat-card {
  background-color: $uni-bg-color;
  border-radius: $radius-xl;
  padding: $spacing-5;
  box-shadow: $shadow-sm;
  display: flex;
  align-items: center;
  gap: $spacing-3;
  transition: all $transition-base;
}

.stat-card:hover {
  box-shadow: $shadow-md;
  transform: translateY(-1rpx);
}

.stat-icon-container {
  width: 64rpx;
  height: 64rpx;
  border-radius: $radius-lg;
  display: flex;
  align-items: center;
  justify-content: center;
  flex-shrink: 0;
}

.stat-icon-container.primary {
  background: linear-gradient(135deg, $primary-100, $primary-200);
}

.stat-icon-container.success {
  background: linear-gradient(135deg, $success-50, $success-100);
}

.stat-icon-container.warning {
  background: linear-gradient(135deg, $warning-50, $warning-100);
}

.stat-icon-container.info {
  background: linear-gradient(135deg, $info-50, $info-100);
}

.stat-icon {
  font-size: $font-size-lg;
}

.stat-content {
  flex: 1;
  min-width: 0;
}

.stat-value {
  display: block;
  font-size: $font-size-lg;
  font-weight: $font-weight-bold;
  color: $uni-text-color;
  line-height: $line-height-tight;
  margin-bottom: $spacing-1;
}

.stat-label {
  display: block;
  font-size: $font-size-xs;
  color: $uni-text-color-secondary;
  line-height: $line-height-normal;
}

/* ==================== 设置与帮助 ==================== */
.settings-section {
  padding: 0 $spacing-4 $spacing-6;
}

.settings-card {
  background-color: $uni-bg-color;
  border-radius: $radius-xl;
  box-shadow: $shadow-sm;
  overflow: hidden;
}

.settings-group {
  display: flex;
  flex-direction: column;
}

.setting-item {
  display: flex;
  align-items: center;
  padding: $spacing-5 $spacing-6;
  border-bottom: 1rpx solid $uni-border-color-light;
  transition: all $transition-base;
  cursor: pointer;
}

.setting-item:last-child {
  border-bottom: none;
}

.setting-item:hover {
  background-color: $uni-bg-color-hover;
}

.setting-item:active {
  background-color: $uni-bg-color-active;
}

.setting-icon-wrapper {
  width: 56rpx;
  height: 56rpx;
  background-color: $neutral-100;
  border-radius: $radius-lg;
  display: flex;
  align-items: center;
  justify-content: center;
  margin-right: $spacing-4;
  flex-shrink: 0;
}

.setting-icon {
  font-size: $font-size-lg;
}

.setting-content {
  flex: 1;
  min-width: 0;
}

.setting-title {
  font-size: $font-size-base;
  font-weight: $font-weight-medium;
  color: $uni-text-color;
  line-height: $line-height-tight;
  margin-bottom: $spacing-1;
}

.setting-desc {
  font-size: $font-size-sm;
  color: $uni-text-color-secondary;
  line-height: $line-height-normal;
}

.setting-arrow {
  font-size: $font-size-xl;
  color: $uni-text-color-tertiary;
  font-weight: $font-weight-light;
  margin-left: $spacing-2;
}

/* ==================== 版本信息 ==================== */
.app-info-section {
  text-align: center;
  padding: $spacing-8 $spacing-4 $spacing-12;
}

.app-version {
  display: block;
  font-size: $font-size-sm;
  color: $uni-text-color-secondary;
  font-weight: $font-weight-medium;
  margin-bottom: $spacing-2;
}

.app-copyright {
  display: block;
  font-size: $font-size-xs;
  color: $uni-text-color-tertiary;
  line-height: $line-height-normal;
}

/* ==================== 响应式适配 ==================== */
@media (max-width: 375px) {
  .quick-actions-grid {
    grid-template-columns: 1fr;
  }

  .stats-cards-grid {
    grid-template-columns: 1fr;
  }

  .user-profile-card {
    flex-direction: column;
    text-align: center;
    gap: $spacing-3;
  }

  .user-details {
    align-items: center;
  }
}

/* ==================== 动画效果 ==================== */
.fade-in {
  animation: fadeIn 0.3s ease-out;
}

@keyframes fadeIn {
  from {
    opacity: 0;
    transform: translateY($spacing-4);
  }

  to {
    opacity: 1;
    transform: translateY(0);
  }
}

.slide-up {
  animation: slideUp 0.4s ease-out 0.1s both;
}

@keyframes slideUp {
  from {
    opacity: 0;
    transform: translateY($spacing-6);
  }

  to {
    opacity: 1;
    transform: translateY(0);
  }
}

.pulse {
  animation: pulse 2s ease-in-out infinite;
}

@keyframes pulse {

  0%,
  100% {
    opacity: 1;
    transform: scale(1);
  }

  50% {
    opacity: 0.8;
    transform: scale(1.1);
  }
}

.loading-spin {
  animation: spin 1s linear infinite;
}

@keyframes spin {
  from {
    transform: rotate(0deg);
  }

  to {
    transform: rotate(360deg);
  }
}

.touch-feedback {
  position: relative;
  overflow: hidden;
}

.touch-feedback::before {
  content: '';
  position: absolute;
  top: 50%;
  left: 50%;
  width: 0;
  height: 0;
  border-radius: 50%;
  background: rgba(99, 102, 241, 0.1);
  transform: translate(-50%, -50%);
  transition: width 0.3s, height 0.3s;
}

.touch-feedback:active::before {
  width: 200%;
  height: 200%;
}
</style>
